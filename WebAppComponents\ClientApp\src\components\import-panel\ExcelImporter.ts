import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/shared/communication-service.ts'
import { v4 as uuid } from 'uuid'

export type ColumnData = {
	key: string
	display: string
	fieldId?: string
	id?: string
	dataType?: string
	columnOrder?: number
}

export type ImportPresetData = {
	label: string
	value: string
	status: boolean
}

export type ExcelImporterType = {
	dataSourceId?: string
	viewId?: string
	pageDataSourceId?: string
	pageViewColumns: ColumnData[]
	importPresets: ImportPresetData[]
	onImportStart?: () => void
	onImportComplete?: (success: boolean, errors?: any[]) => void
	onImportProgress?: (progress: number, status: string) => void
}

export type ExcelData = {
	data: any[][]
	sheetNames: string[]
	currentSheet: string
}

export type FieldMapping = {
	columnKey: string
	index: number
	dataType?: string
}

export type ImportConfiguration = {
	isFirstLineHeader: boolean
	isDuplicateCheckEnabled: boolean
	actionForDuplicates: string | null
	selectedMappings: FieldMapping[]
}

export type ImportResult = {
	summary: {
		inserted: number
		updated: number
		skipped: number
		failed: number
	}
	errors: ImportError[]
	duplicates: DuplicateItem[]
	fileEntityId?: string
	errorsExcelBase64?: string
}

export type DuplicateItem = {
	columnKey: string
	value: string
	tooltipData?: {
		column: string
		currentData: string
		newData: string
	}
}

export type ImportError = {
	rowNumber: number
	error: string
	columnKey?: string
	value?: any
}

export enum DialogStep {
	PRESET = 'preset',
	PREVIEW = 'preview',
	COLUMN_MAPPING = 'column-mapping',
	ERROR = 'error',
	DUPLICATE = 'duplicate',
	CLOSED = 'closed'
}

/**
 * Excel Importer web component using LIT (https://lit.dev)
 */
@customElement('lvl-excel-importer')
export class ExcelImporter extends LitElement implements ExcelImporterType {

	static styles = [
		styles.base,
		styles.color,
		fontAwesome,
		css`
			:host {
				display: block;
			}

			/* Section styling fixes */
			lvl-section {
				--section-box-shadow: none !important;
			}

			/* Excel Importer Dialog Styles */
			.import-dialog {
				display: block;
				--width: 10rem;
			}

			.import-dialog-wide {
				display: block;
				--width: 10rem;
			}

			.import-dialog-extra-wide {
				display: block;
				--width: 10rem;
			}

			.import-dialog-content {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding: 1rem;
				margin-left: 1.4rem;
				margin-top: 1.4rem;
				margin-bottom: 1.4rem;
				border: 0.1rem solid var(--clr-border);
				border-radius: var(--size-radius-m);
				background-color: var(--clr-background-lvl-0);
			}

			.import-preset-content {
				display: flex;
				flex-direction: column;
				padding: 1rem;
				margin-left: 1.4rem;
				margin-top: 1.4rem;
				margin-bottom: 1.4rem;
				border: 0.1rem solid var(--clr-border);
				border-radius: var(--size-radius-m);
				background-color: var(--clr-background-lvl-0);
			}

			.import-info-section {
				display: flex;
				align-items: center;
				margin-bottom: 2rem;
				width: 100%;
				justify-content: flex-start;
				color: var(--clr-text-secondary) !important;
			}

			.import-info-icon {
				margin-right: 1rem;
				color: var(--clr-text-secondary-positiv);
				font-size: 1.1rem;
			}

			.import-info-text {
				color: var(--clr-text-secondary-positiv);
				font-size: 1.1rem;
			}

			.import-preview-container {
				padding: 0.2rem;
				width: 100%;
				height: 100%;
				overflow-y: auto;
				flex-grow: 1;
				display: flex;
				gap: 1.4rem;
				min-height: 10rem;
				padding-right: 0;
			}

			.import-preview-content {
				flex: 1;
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
				background-color: var(--clr-background-lvl-0);
				border-radius: 0.4rem;
				box-shadow: 0 0.4rem 0.8rem rgba(0, 0, 0, 0.1);
				margin: 1.4rem 0 1.4rem 1.4rem;
				padding: 1rem;
			}

			.import-mapping-container {
				padding: 0.2rem;
				width: 100%;
				height: 100%;
				overflow-y: auto;
				flex-grow: 1;
				display: flex;
				gap: 1.4rem;
				min-height: 10rem;
				padding-right: 0;
			}

			.import-mapping-content {
				flex: 1;
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
				background-color: var(--clr-background-lvl-0);
				border-radius: 0.4rem;
				box-shadow: 0 0.4rem 0.8rem rgba(0, 0, 0, 0.1);
				margin: 1.4rem 0 1.4rem 1.4rem;
				padding: 1rem;
			}

			.import-section {
				max-height: 100%;
				height: max-content;
				flex: 1;
			}

			.hidden-option {
				display: none !important;
			}

			/* Smooth transition for duplicate actions visibility */
			div[data-action="duplicate-actions"] {
				transition: none !important;
			}

			/* Error Dialog Styles */
			.import-error-dialog {
				display: block;
			}

			.import-error-content {
				padding-right: 0;
			}

			.import-error-card {
				flex: 1;
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
				background-color: var(--clr-background-lvl-0);
				border-radius: 0.4rem;
				margin: 1.4rem 0 1.4rem 1.4rem;
				padding: 1rem;
			}

			/* Duplicate Dialog Styles */
			.import-duplicate-dialog {
				display: block;
				--width: 60rem;
			}

			.import-duplicate-container {
				padding: 0.2rem;
				width: 100%;
				height: 100%;
				overflow-y: auto;
				flex-grow: 1;
				display: flex;
				gap: 1.4rem;
				min-height: 10rem;
				padding-right: 0;
			}

			.import-duplicate-content {
				flex: 1;
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
				background-color: var(--clr-background-lvl-0);
				border-radius: 0.4rem;
				box-shadow: 0 0.4rem 0.8rem rgba(0, 0, 0, 0.1);
				margin: 1.4rem 0 1.4rem 1.4rem;
				padding: 1rem;
			}

			.import-duplicate-button-container {
				text-align: left;
				margin-bottom: 1rem;
			}

			.import-duplicate-table {
				width: 100%;
				height: 100%;
				border: none;
				border-collapse: collapse;
				background: var(--clr-background-lvl-0);
			}

			.import-button-group {
				margin-top: 0.1rem;
				margin-left: 0.1rem;
				margin-bottom: 0.4rem;
			}

			.import-button-height {
				height: 3.3rem;
			}

			.import-button-group-margin {
				margin: 1.4rem 0 1rem;
			}

			/* Import button styles */
			.import-button-group {
				margin-top: 0.1rem;
				margin-left: 0.1rem;
				margin-bottom: 0.4rem;
			}

			.import-button-height {
				height: 3.3rem;
			}

			.import-button-group-margin {
				margin: 1.4rem 0 1rem;
			}

			/* Duplicate check section styles */
			.import-duplicate-toggle {
				margin-bottom: 0.7rem;
				display: flex;
				align-items: center;
			}

			.import-toggle-label {
				display: inline-block;
				margin-right: 1rem;
			}

			.import-toggle-input {
				transform: scale(1.2);
				display: inline-block;
			}

			.import-action-label {
				margin-bottom: 0.2rem;
			}

			/* Autocomplete styles for column mapping */
			.import-autocomplete {
				display: inline-block;
				width: 20rem;
			}

			/* Fix table column widths - more aggressive approach */
			.import-section lvl-table {
				table-layout: fixed !important;
				width: 100% !important;
			}

			.import-section lvl-table::part(table) {
				table-layout: fixed !important;
				width: 100% !important;
			}

			/* Force column widths in shadow DOM - more specific selectors */
			.import-section lvl-table::part(header-cell):nth-child(1),
			.import-section lvl-table::part(cell):nth-child(1) {
				width: auto !important;
				min-width: 15rem !important;
			}

			.import-section lvl-table::part(header-cell):nth-child(2),
			.import-section lvl-table::part(cell):nth-child(2) {
				width: 3rem !important;
				min-width: 3rem !important;
				max-width: 3rem !important;
			}

			.import-section lvl-table::part(header-cell):nth-child(3),
			.import-section lvl-table::part(cell):nth-child(3) {
				width: 20rem !important;
				min-width: 20rem !important;
				max-width: 20rem !important;
			}

			.import-section lvl-table::part(header-cell):nth-child(4),
			.import-section lvl-table::part(cell):nth-child(4) {
				width: 3rem !important;
				min-width: 3rem !important;
				max-width: 3rem !important;
			}

			.import-section lvl-table::part(header-cell):nth-child(5),
			.import-section lvl-table::part(cell):nth-child(5) {
				width: auto !important;
				min-width: 15rem !important;
			}

			/* Force autocomplete width inside table cells */
			.import-section lvl-table lvl-autocomplete,
			lvl-dialog[name="import-dialog"] lvl-autocomplete {
				width: 18rem !important;
				min-width: 18rem !important;
				max-width: 18rem !important;
				display: inline-block !important;
			}

			/* Global table column width enforcement for column mapping */
			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless] {
				table-layout: fixed !important;
				width: 100% !important;
				visibility: visible !important;
				opacity: 1 !important;
			}

			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(table) {
				table-layout: fixed !important;
				width: 100% !important;
			}

			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(header-cell):nth-child(1),
			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(cell):nth-child(1) {
				width: auto !important;
				min-width: 15rem !important;
			}

			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(header-cell):nth-child(2),
			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(cell):nth-child(2) {
				width: 3rem !important;
				min-width: 3rem !important;
				max-width: 3rem !important;
			}

			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(header-cell):nth-child(3),
			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(cell):nth-child(3) {
				width: 20rem !important;
				min-width: 20rem !important;
				max-width: 20rem !important;
			}

			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(header-cell):nth-child(4),
			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(cell):nth-child(4) {
				width: 3rem !important;
				min-width: 3rem !important;
				max-width: 3rem !important;
			}

			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(header-cell):nth-child(5),
			lvl-dialog[name="import-dialog"] lvl-table[selection-mode="multiple"][borderless]::part(cell):nth-child(5) {
				width: auto !important;
				min-width: 15rem !important;
			}

			/* Additional styling for the specific table class */
			.import-column-mapping-table {
				table-layout: fixed !important;
				width: 100% !important;
			}

			.import-column-mapping-table::part(table) {
				table-layout: fixed !important;
				width: 100% !important;
			}

			.import-column-mapping-table lvl-autocomplete {
				width: 18rem !important;
				min-width: 18rem !important;
				max-width: 18rem !important;
				display: inline-block !important;
				box-sizing: border-box !important;
				transition: none !important;
			}

			/* Prevent any visual jumping during table rendering */
			.import-column-mapping-table * {
				transition: none !important;
			}

			/* Reduce spacing for info section in column mapping */
			.import-section .import-info-section {
				margin-bottom: 1rem;
			}
		`,
	]

	//#region attributes

	@property({ attribute: 'data-source-id' })
	dataSourceId?: string

	@property({ attribute: 'view-id' })
	viewId?: string

	@property({ attribute: 'page-data-source-id' })
	pageDataSourceId?: string

	@property({ type: Array })
	pageViewColumns: ColumnData[] = []

	@property({ type: Array })
	importPresets: ImportPresetData[] = []

	@property({ type: Function })
	onImportStart?: () => void

	@property({ type: Function })
	onImportComplete?: (success: boolean, errors?: any[]) => void

	@property({ type: Function })
	onImportProgress?: (progress: number, status: string) => void

	@property({ attribute: 'data-source-selector' })
	dataSourceSelector?: string = 'lvl-data-source'

	@property({ attribute: 'dialog-container' })
	dialogContainer?: string = 'body'

	@property({ attribute: 'overlay-service' })
	overlayService?: string = 'window.Overlay'

	@property({ attribute: 'toaster-service' })
	toasterService?: string = '#toaster'

	//#endregion

	//#region states

	@state()
	private _currentDialogStep: DialogStep = DialogStep.CLOSED

	@state()
	private _excelData?: ExcelData

	@state()
	private _duplicateData?: DuplicateItem[]

	@state()
	private _duplicateSummary?: any

	@state()
	private _importErrors?: any[]

	@state()
	private _importConfiguration: ImportConfiguration = {
		isFirstLineHeader: true,
		isDuplicateCheckEnabled: true,
		actionForDuplicates: null,
		selectedMappings: []
	}

	//#endregion

	//#region private properties

	private static readonly _localizer: StringLocalizer = new StringLocalizer('ExcelImporter')
	private _abortController?: AbortController
	private _selectedValues = new Set<string>()
	private _mutationObserver?: MutationObserver
	private _dropdownObserver?: MutationObserver
	private _progressSocket?: any
	private _originalFileName?: string
	private _errorsExcelBase64?: string
	private _fileEntityId?: string
	private _componentClickHandlers: EventListener[] = []
	private _componentEventListeners: { element: Element | Document, event: string, handler: EventListener }[] = []
	private _isImporting: boolean = false
	private _timeoutIds: any[] = []
	private _documentEventListeners: { event: string, handler: EventListener }[] = []

	private localize(key: string, ...replacements: any[]): string {
		return ExcelImporter._localizer.localize(key, ...replacements)
	}

	//#endregion

	//#region lifecycle callbacks

	connectedCallback() {
		super.connectedCallback()
		this._abortController = new AbortController()
	}

	disconnectedCallback() {
		super.disconnectedCallback()
		this._abortController?.abort()

		// Clean up component event listeners
		this.cleanupComponentEventListeners()

		// Clean up document event listeners
		this._cleanupDocumentEventListeners()

		// Clear all timeouts
		this._clearAllTimeouts()

		// Close any open dialogs
		this.closeCurrentDialog()

		this.cleanupObservers()

		// Clean up any autocomplete handling when component is removed
		this.cleanupAutocompleteHandling()

		// Clean up progress socket if it exists
		if (this._progressSocket) {
			this._progressSocket.destroy()
			this._progressSocket = undefined
		}
	}

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)
		this.initializeColumns()
	}

	protected updated(changedProperties: PropertyValues) {
		super.updated(changedProperties)

		// Set up event listeners when column mapping dialog opens
		if (changedProperties.has('_currentDialogStep')) {
			if (this._currentDialogStep === DialogStep.COLUMN_MAPPING) {
			// Try multiple times with increasing delays to find the dialog
			const trySetupEventListeners = (attempt: number = 1) => {
				// Check all possible dialog selectors
				const dialogSelectors = [
					'lvl-dialog[name="import-dialog"]',
					'lvl-dialog.import-dialog',
					'lvl-dialog'
				]

				let dialogBox: Element | null = null
				for (const selector of dialogSelectors) {
					dialogBox = document.querySelector(selector)
					if (dialogBox) break
				}

				// Also check within this component's shadow root
				if (!dialogBox && this.shadowRoot) {
					for (const selector of dialogSelectors) {
						dialogBox = this.shadowRoot.querySelector(selector)
						if (dialogBox) break
					}
				}

				if (dialogBox) {
					// Set up global reference for HTML onchange attribute - THIS IS THE KEY!
					;(window as any).excelImporterInstance = this

					// Apply table width fixes immediately before any other setup
					this.fixTableColumnWidths(dialogBox)

					this.setupColumnMappingEventListeners()
					this.setupDropdownObserver()

				} else if (attempt < 5) {
					// Try again with longer delay
					this._addTimeout(() => trySetupEventListeners(attempt + 1), attempt * 200)
				}
			}

			// Start trying immediately
			trySetupEventListeners()
		} else {
			// Dialog closed, stop polling and cleanup
			// Clean up global reference
			if ((window as any).excelImporterInstance === this) {
				;(window as any).excelImporterInstance = null
			}
		}
	}
}

	//#endregion

	protected render() {

		return html`
			<div>
				<!-- Excel Importer Component - Use startImport() method to begin import process -->
				<slot></slot>

				<!-- Single Dynamic Dialog -->
				<lvl-dialog
					class="${this._getDialogClass()}"
					heading="${this._getDialogHeading()}"
					icon="${this._getDialogIcon()}"
					name="import-dialog"
					width="${this._getDialogWidth()}"
					?open="${this._currentDialogStep !== DialogStep.CLOSED}">
					${this._renderDialogContent()}
					${this._renderDialogButtons()}
				</lvl-dialog>
			</div>
		`
	}


	//#region dialog helper methods

	private _getDialogClass(): string {
		switch (this._currentDialogStep) {
			case DialogStep.PRESET:
				return 'import-dialog'
			case DialogStep.PREVIEW:
				return 'import-dialog'
			case DialogStep.COLUMN_MAPPING:
				return 'import-dialog-extra-wide'
			case DialogStep.ERROR:
				return 'import-error-dialog'
			case DialogStep.DUPLICATE:
				return 'import-duplicate-dialog'
			default:
				return 'import-dialog'
		}
	}

	private _getDialogHeading(): string {
		switch (this._currentDialogStep) {
			case DialogStep.PRESET:
				return this.localize('import', 'Import')
			case DialogStep.PREVIEW:
				return this.localize('importConfiguration')
			case DialogStep.COLUMN_MAPPING:
				return this.localize('columnMapping')
			case DialogStep.ERROR:
				return this.localize('importReport', 'Import Report')
			case DialogStep.DUPLICATE:
				return this.localize('importDuplicateResolution', 'Import Duplicate Resolution')
			default:
				return ''
		}
	}

	private _getDialogIcon(): string {
		switch (this._currentDialogStep) {
			case DialogStep.PRESET:
				return 'file-import'
			case DialogStep.PREVIEW:
				return ''
			case DialogStep.COLUMN_MAPPING:
				return 'cog'
			case DialogStep.ERROR:
				return 'file-chart-column'
			case DialogStep.DUPLICATE:
				return 'copy'
			default:
				return ''
		}
	}

	private _getDialogWidth(): string {
		switch (this._currentDialogStep) {
			case DialogStep.PRESET:
				return '450'
			case DialogStep.PREVIEW:
				return '650'
			case DialogStep.COLUMN_MAPPING:
				return '700'
			case DialogStep.ERROR:
				return '700'
			case DialogStep.DUPLICATE:
				return '600'
			default:
				return '450'
		}
	}

	private _renderDialogContent() {
		switch (this._currentDialogStep) {
			case DialogStep.PRESET:
				return this._renderPresetContent()
			case DialogStep.PREVIEW:
				return this._renderPreviewContent()
			case DialogStep.COLUMN_MAPPING:
				return this._renderColumnMappingContent()
			case DialogStep.ERROR:
				return this._renderErrorContent()
			case DialogStep.DUPLICATE:
				return this._renderDuplicateContent()
			default:
				return html``
		}
	}

	private _renderDialogButtons() {
		switch (this._currentDialogStep) {
			case DialogStep.PRESET:
				return html`
					<lvl-button slot="button-left" data-action="cancel" color="info" initdone
						label="${this.localize('cancel')}" @click="${this._handlePresetCancel}"></lvl-button>
					<lvl-button slot="button-right" data-action="next" type="primary" initdone
						label="${this.localize('next')}" @click="${this._handlePresetNext}"></lvl-button>
				`
			case DialogStep.PREVIEW:
				return html`
					<lvl-button slot="button-left" data-action="cancel-preview" color="info" initdone
						label="${this.localize('cancel')}" @click="${this._handlePreviewCancel}"></lvl-button>
					<lvl-button slot="button-right" data-action="next" type="primary" initdone
						label="${this.localize('next')}" @click="${this._handlePreviewNext}"></lvl-button>
				`
			case DialogStep.COLUMN_MAPPING:
				return html`
					<lvl-button slot="button-left" data-action="cancel" color="info" initdone
						label="${this.localize('cancel')}" @click="${this._handleColumnMappingCancel}"></lvl-button>
					<lvl-button slot="button-right" data-action="import" type="primary" initdone
						label="${this.localize('import')}" @click="${this._handleColumnMappingImport}"></lvl-button>
				`
			case DialogStep.ERROR:
				return html`
					<lvl-button slot="button-left" color="info" label="${this.localize('close', 'Close')}" @click="${this._handleErrorDialogClose}"></lvl-button>
					<lvl-button slot="button-right" type="secondary" color="active" label="${this._errorsExcelBase64 ? this.localize('downloadExcelReport', 'Download Excel Report') : this.localize('downloadReport', 'Download Report')}" @click="${this._handleDownloadReport}"></lvl-button>
					<lvl-button slot="button-right" type="primary" label="${this.localize('tryAgain', 'Try Again')}" @click="${this._handleTryAgain}"></lvl-button>
				`
			case DialogStep.DUPLICATE:
				return html`
					<lvl-button slot="button-left" color="info" label="${this.localize('skipAll', 'Skip All')}" @click="${this._handleSkipAll}"></lvl-button>
					<lvl-button slot="button-right" type="primary" label="${this.localize('continueImport', 'Continue Import')}" @click="${this._handleContinueImport}"></lvl-button>
				`
			default:
				return html``
		}
	}

	private _renderPresetContent() {
		return html`
			<div class="import-preset-content">
				<div class="import-info-section">
					<i class="fas fa-info-circle import-info-icon"></i>
					<span class="import-info-text">${this.localize('pickImportPresetMessage', 'Pick an import preset or choose custom to configure import manually')}</span>
				</div>
				<lvl-selectable-cards options='${JSON.stringify(this.importPresets).replace(/'/g, "&#39;")}'></lvl-selectable-cards>
			</div>
		`
	}

	private _renderPreviewContent() {
		return html`
			<div class="import-preview-container">
				<div class="import-preview-content">
					<label>${this.localize('isFirstLineHeader')}</label>
					<lvl-button-group class="import-button-group-margin" name="defaultGroup" value="YES">
						<lvl-button id="yes-button" size="large" label="${this.localize('yes')}" value="YES" class="import-button-height" @click="${this._handleHeaderYes}"></lvl-button>
						<lvl-button id="no-button" size="large" label="${this.localize('no')}" value="NO" class="import-button-height" @click="${this._handleHeaderNo}"></lvl-button>
					</lvl-button-group>
					<lvl-section class="import-section" heading="${this.localize('tablePreview')}" max-height="30rem" calc-height borderless>
						${this._excelData ? this._renderPreviewTable() : ''}
					</lvl-section>
				</div>
			</div>
		`
	}

	private _renderColumnMappingContent() {
		return html`
			<div class="import-mapping-container">
				<div class="import-mapping-content">
					<lvl-section class="import-section" heading="${this.localize('checkForDuplicates', 'Check for Duplicates')}" max-height="24rem" calc-height ignore-overflow borderless>
						<div class="import-info-section">
							<i class="fas fa-info-circle import-info-icon"></i>
							<span class="import-info-text">${this.localize('duplicatesInfoMessage', 'If active, the imported elements will be checked for duplicates. When duplicates are found they will be updated or skipped, depending on your choice of action.')}</span>
						</div>
						<div class="import-duplicate-toggle" id="duplicate-toggle">
							<label for="enable-duplicates-toggle" class="import-toggle-label">${this.localize('enableDuplicateCheck', 'Enable Duplicate Check')}</label>
							<lvl-toggle name="enable-duplicates-toggle" class="import-toggle-input" value="${this._importConfiguration.isDuplicateCheckEnabled ? 'true' : 'false'}" @change="${this._handleDuplicateToggle}"></lvl-toggle>
						</div>
						<div data-action="duplicate-actions" class="${this._importConfiguration.isDuplicateCheckEnabled ? '' : 'hidden-option'}">
							<label class="import-action-label">${this.localize('action', 'Action')}</label>
							<lvl-button-group class="import-button-group" name="duplicate-action" value="${this._importConfiguration.actionForDuplicates || 'PerElement'}">
								<lvl-button id="choose-per-element" size="large" label="${this.localize('choosePerElement', 'Choose per Element')}" value="PerElement" class="import-button-height" @click="${this._handleDuplicateAction}"></lvl-button>
								<lvl-button id="skip-import" size="large" label="${this.localize('skipImportOfElement', 'Skip import of Element')}" value="Skip" class="import-button-height" @click="${this._handleDuplicateAction}"></lvl-button>
								<lvl-button id="update-matching" size="large" label="${this.localize('updateMatchingElement', 'Update matching Element')}" value="Update" class="import-button-height" @click="${this._handleDuplicateAction}"></lvl-button>
							</lvl-button-group>
						</div>
					</lvl-section>

					<lvl-section class="import-section" heading="${this.localize('importDataInformation', 'Import data information')}" max-height="30rem" calc-height borderless>
						<div class="import-info-section">
							<i class="fas fa-info-circle import-info-icon"></i>
							<span class="import-info-text">${this.localize('importDataInfoMessage', 'You can manually match the imported columns to the fields they should fill or skip columns you don\'t want to import.')}</span>
						</div>
						<div>
							${this._renderColumnMappingTable()}
						</div>
					</lvl-section>
				</div>
			</div>
		`
	}

	private _renderErrorContent() {
		return html`
			<div class="import-error-content">
				<div class="import-error-card">
					<lvl-section class="import-section" borderless heading="${this.localize('reports', 'Reports')}" max-height="450" calc-height >
						<lvl-table rows='${JSON.stringify(this._importErrors || [])}'>
							<lvl-table-data-column name="columnKey" label="${this.localize('element', 'Element')}"></lvl-table-data-column>
							<lvl-table-data-column name="rowNumber" label="${this.localize('lineNumber', 'Line Number')}"></lvl-table-data-column>
							<lvl-table-data-column name="error" label="${this.localize('errorDescription', 'Error Description')}"></lvl-table-data-column>
						</lvl-table>
					</lvl-section>
				</div>
			</div>
		`
	}

	private _renderDuplicateContent() {
		return html`
			<div class="import-duplicate-container">
				<div class="import-duplicate-content">
					<lvl-section class="import-section" heading="${this.localize('duplicates', 'Duplicates')}" max-height="450" calc-height ignore-overflow borderless>
						<div class="import-info-section">
							<i class="fas fa-info-circle import-info-icon"></i>
							<span class="import-info-text">${this.localize('duplicatesListInfoMessage', 'The list below is the overlapping data, that was found.')}</span>
						</div>
						<div class="import-duplicate-button-container">
							<lvl-button color="info" label="${this.localize('updateAll', 'Update all')}" @click="${this._handleUpdateAll}"></lvl-button>
						</div>
						<lvl-table borderless rows='${JSON.stringify(this._duplicateData || [])}' class="import-duplicate-table">
							<lvl-table-data-column name="columnKey" label="${this.localize('element', 'Element')}"></lvl-table-data-column>
							<lvl-table-data-column name="tooltipInfo" type="component"></lvl-table-data-column>
							<lvl-table-data-column label="${this.localize('actionForDuplicates', 'Action For Duplicates')}" type="component" width="320">
								<lvl-button-group class="import-button-group" name="defaultGroup" value="Skip">
									<lvl-button size="large" label="${this.localize('skipImportOfElement', 'Skip import of Element')}" value="Skip" class="import-button-height"></lvl-button>
									<lvl-button size="large" label="${this.localize('updateMatchingElement', 'Update matching Element')}" value="Update" class="import-button-height"></lvl-button>
								</lvl-button-group>
							</lvl-table-data-column>
						</lvl-table>
					</lvl-section>
				</div>
			</div>
		`
	}

	//#endregion

	//#region public methods

	/**
	 * Start the import process by opening file dialog
	 */
	public async startImport(): Promise<void> {
		try {
			// Ensure XLSX library is loaded
			await this.ensureXLSXLoaded()

			// Initialize columns if not already done
			await this.initializeColumns()

			// Create and trigger file input
			this.openFileDialog()
		} catch (error) {
			this.showErrorToast(this.localize('importErrorGeneric'))
		}
	}

	/**
	 * Handle file upload from external source
	 */
	public async handleFileUpload(file: File): Promise<void> {
		try {
			await this.ensureXLSXLoaded()
			await this.processExcelFile(file)
		} catch (error) {
			this.showErrorToast(this.localize('importErrorGeneric'))
		}
	}



	//#endregion

	//#region private methods

	private async initializeColumns(): Promise<void> {

		if (this.pageViewColumns.length > 0) {
			return // Already initialized
		}

		if (!this.viewId) {
			const pageViewSection = document.querySelector('section.page-view[data-view-type="List"]')
			this.viewId = pageViewSection?.getAttribute('data-view-id') || undefined
		}

		if (this.viewId) {
			const columns = await this.fetchColumns()
			this.pageViewColumns = [...columns.visibleColumns, ...columns.invisibleColumns]
		}
	}

	private async fetchColumns(): Promise<{ visibleColumns: ColumnData[], invisibleColumns: ColumnData[] }> {
		try {
			if (!this.viewId) {
				return { visibleColumns: [], invisibleColumns: [] }
			}

			const response = await CommunicationServiceProvider.get(`/Api/PageViews/${this.viewId}/Columns`)

			if (response.state !== CommunicationResponseType.Ok) {
				throw new Error(`API request failed with status ${response.state}`)
			}

			const data = response.data

			// Handle the actual API response format
			if (data && data.visibleColumns && data.invisibleColumns) {
				const visibleColumns = data.visibleColumns.map((col: any) => ({
					key: col.key,
					display: col.display || col.key,
					fieldId: col.fieldId,
					dataType: col.fieldType
				}))

				const invisibleColumns = data.invisibleColumns.map((col: any) => ({
					key: col.key,
					display: col.display || col.key,
					fieldId: col.fieldId,
					dataType: col.fieldType
				}))

				// Store the data source ID globally
				if (data.dataSourceId) {
					this.dataSourceId = data.dataSourceId
				}

				return { visibleColumns, invisibleColumns }
			} else {
				return { visibleColumns: [], invisibleColumns: [] }
			}
		} catch (error) {
			return { visibleColumns: [], invisibleColumns: [] }
		}
	}

	private openFileDialog(): void {
		// Emit event for parent to handle file dialog instead of accessing document directly
		this.dispatchEvent(new CustomEvent('open-file-dialog', {
			detail: {
				accept: '.xlsx, .xls',
				callback: (file: File) => this.processExcelFile(file)
			},
			bubbles: true,
			composed: true
		}))

		// Fallback to direct file input creation if no parent handles the event
		setTimeout(() => {
			const fileInput = document.createElement('input')
			fileInput.type = 'file'
			fileInput.accept = '.xlsx, .xls'
			fileInput.style.display = 'none'

			fileInput.addEventListener('change', async (event) => {
				const target = event.target as HTMLInputElement
				const file = target.files?.[0]

				if (file) {
					await this.processExcelFile(file)
				}
			})

			document.body.appendChild(fileInput)
			fileInput.click()

			setTimeout(() => {
				if (document.body.contains(fileInput)) {
					document.body.removeChild(fileInput)
				}
			}, 1000)
		}, 0)
	}

	private async processExcelFile(file: File): Promise<void> {
		try {
			this.showOverlay('Processing Excel file...')

			// Read Excel file
			this._excelData = await this.readExcelFile(file)

			// Validate data
			const validation = this.validateExcelData(this._excelData)
			if (!validation.isValid) {
				this.showErrorToast(validation.errors.join(', '))
				return
			}

			// Store original filename
			this._originalFileName = file.name

			// Check for presets first - following exact original workflow
			this.showOverlay(this.localize('loadingAvailablePresets', 'Loading available presets...'))

			const hasPresets = await this.loadImportPresets()

			if (hasPresets) {
				this.showPresetDialog()
			} else {
				this.showPreviewDialog()
			}
		} catch (error) {
			this.showErrorToast(this.localize('importErrorGeneric'))
		} finally {
			this.hideOverlay()
		}
	}

	private async loadImportPresets(): Promise<boolean> {
		try {
			const dataSourceId = this.getDataSourceId()

			if (!dataSourceId) {
				return false
			}

			const apiUrl = `/Api/ExcelPresets/${dataSourceId}/PerDataSource`

			// Use direct fetch like the working old implementation
			const response = await fetch(apiUrl)

			if (!response.ok) {
				return false
			}

			const presetsData = await response.json()

			if (presetsData && Array.isArray(presetsData) && presetsData.length > 0) {
				const formattedPresets = presetsData.map((item: any) => ({
					label: item.name.replace(/'/g, ""),
					value: item.id,
					status: item.isActive
				}))

				// Add the "Custom" option
				formattedPresets.push({
					label: "Custom",
					value: "custom",
					status: true
				})

				this.importPresets = formattedPresets

				// Store globally for compatibility with original workflow
				;(window as any).formPresetsData = formattedPresets
				return true
			}

			// If no presets are found, return false to go directly to preview dialog
			// This matches the original implementation behavior
			return false
		} catch (error) {
			return false
		}
	}

	private getDataSourceId(): string {
		// Emit event for parent to provide data source ID instead of accessing document directly
		let dataSourceId = this.dataSourceId || this.pageDataSourceId

		if (!dataSourceId) {
			const event = new CustomEvent('get-data-source-id', {
				detail: { callback: (id: string) => { dataSourceId = id } },
				bubbles: true,
				composed: true
			})
			this.dispatchEvent(event)

			// Fallback to document query if no parent handles the event
			if (!dataSourceId) {
				dataSourceId = document.querySelector(this.dataSourceSelector || 'lvl-data-source')?.getAttribute('data-id') || ''
			}
		}

		return dataSourceId
	}



	private cleanupComponentEventListeners(): void {
		// Remove all tracked component event listeners
		this._componentEventListeners.forEach(({ element, event, handler }) => {
			element.removeEventListener(event, handler)
		})
		this._componentEventListeners = []

		this._componentClickHandlers.forEach(handler => {
			this.removeEventListener('mousedown', handler)
		})
		this._componentClickHandlers = []
	}

	private _addComponentEventListener(element: Element | Document, event: string, handler: EventListener): void {
		element.addEventListener(event, handler)
		this._componentEventListeners.push({ element, event, handler })
	}

	private _addDocumentEventListener(event: string, handler: EventListener): void {
		document.addEventListener(event, handler, true)
		this._documentEventListeners.push({ event, handler })
	}

	private _addTimeout(callback: () => void, delay: number): any {
		const timeoutId = setTimeout(callback, delay)
		this._timeoutIds.push(timeoutId)
		return timeoutId
	}

	private _clearAllTimeouts(): void {
		this._timeoutIds.forEach(id => clearTimeout(id))
		this._timeoutIds = []
	}

	private _cleanupDocumentEventListeners(): void {
		this._documentEventListeners.forEach(({ event, handler }) => {
			document.removeEventListener(event, handler, true)
		})
		this._documentEventListeners = []
	}

	private fixTableColumnWidths(dialogBox: Element | null): void {
		if (!dialogBox) return

		// Find the table within the dialog
		const table = dialogBox.querySelector('lvl-table.import-column-mapping-table')
		if (!table) return

		// Force table layout immediately
		;(table as any).style.tableLayout = 'fixed'
		;(table as any).style.width = '100%'

		// Apply width directly to autocomplete elements immediately
		const applyAutocompleteStyles = () => {
			const autocompletes = table.querySelectorAll('lvl-autocomplete')
			autocompletes.forEach((autocomplete: any) => {
				autocomplete.style.width = '18rem'
				autocomplete.style.minWidth = '18rem'
				autocomplete.style.maxWidth = '18rem'
				autocomplete.style.display = 'inline-block'
				autocomplete.style.boxSizing = 'border-box'
			})
		}

		// Apply immediately
		applyAutocompleteStyles()

		// Also apply after a micro-task to catch any dynamically added elements
		setTimeout(applyAutocompleteStyles, 0)
	}

	private _renderPreviewTable() {
		if (!this._excelData) return ''

		const previewData = this.generatePreviewData(this._excelData, this._importConfiguration.isFirstLineHeader)
		const tableRows = previewData.rows

		// Create column templates using Lit's html template system
		const tableColumns = previewData.headers.map((header, index) => html`
			<lvl-table-data-column name="col${index}" label="${header}"></lvl-table-data-column>
		`)

		return html`
			<lvl-table rows='${JSON.stringify(tableRows)}' style="border: none;">
				${tableColumns}
			</lvl-table>
		`
	}

	private _handlePresetCancel(): void {
		this._currentDialogStep = DialogStep.CLOSED
	}

	private async _handlePresetNext(): Promise<void> {
		const selectableCards = this.shadowRoot?.querySelector('lvl-selectable-cards')
		const selectedPresetOption = selectableCards?.getAttribute('selected-card') || (selectableCards as any)?.selectedCard

		if (!selectedPresetOption || selectedPresetOption.trim() === '') {
			this.showErrorToast(this.localize('pleaseSelectImportPreset', 'Please select an import preset before proceeding'))
			return
		}

		if (selectedPresetOption === 'custom') {
			this._currentDialogStep = DialogStep.PREVIEW
		} else {
			// Handle preset selection
			await this.handlePresetSelection(selectedPresetOption)
		}
	}

	private async handlePresetSelection(presetId: string): Promise<void> {
		try {
			// Load preset configuration and proceed directly to import (like old implementation)
			this.showOverlay(this.localize('loadingPresetColumns', 'Loading preset columns...'))

			const presetColumns = await this.loadPresetColumns(presetId)
			this.hideOverlay()

			if (!presetColumns || presetColumns.length === 0) {
				this.showErrorToast(this.localize('noColumnsFoundForPreset', 'No columns found for the selected preset'))
				this._currentDialogStep = DialogStep.PREVIEW
				return
			}

			// Store preset columns globally for compatibility with original workflow
			;(window as any).selectedMappings = presetColumns
			;(window as any).columnKeys = presetColumns.map((mapping: any) => mapping.columnKey)

			// Apply preset mappings to our configuration
			this._importConfiguration.selectedMappings = presetColumns.map((col: any) => ({
				columnKey: col.columnKey,
				index: col.index,
				dataType: col.dataType || ''
			}))

			// Add preset column values to the tracking set
			presetColumns.forEach((column: any) => {
				if (column.columnKey && column.columnKey.trim() !== '') {
					this._selectedValues.add(column.columnKey)
				}
			})

			// When a non-custom preset is selected, go directly to import process
			// No need for column mapping dialog since preset columns are already defined
			await this.executeImport()
		} catch (error) {
			this.hideOverlay()
			this.showErrorToast(this.localize('failedToLoadPresetColumns', 'Failed to load preset columns. Please try again.'))
			this._currentDialogStep = DialogStep.PREVIEW
		}
	}

	private async loadPresetColumns(presetId: string): Promise<FieldMapping[]> {
		try {
			const apiUrl = `/Api/ExcelPresets/${presetId}/PerDataSource/Columns`

			// Use direct fetch like the working old implementation
			const response = await fetch(apiUrl)

			if (!response.ok) {
				throw new Error('Failed to load preset columns')
			}

			const columnsData = await response.json()

			if (columnsData && Array.isArray(columnsData)) {
				return columnsData.map((item: any) => ({
					columnKey: item.columnName,
					index: item.columnOrder,
					dataType: item.dataType
				}))
			}

			return []
		} catch (error) {
			return []
		}
	}

	private _handlePreviewCancel(): void {
		if (this.importPresets && this.importPresets.length > 0) {
			this._currentDialogStep = DialogStep.PRESET
		} else {
			this._currentDialogStep = DialogStep.CLOSED
		}
	}

	private _handlePreviewNext(): void {
		this._currentDialogStep = DialogStep.COLUMN_MAPPING
	}

	private _handleHeaderYes(): void {
		this._importConfiguration.isFirstLineHeader = true
		this.requestUpdate()
	}

	private _handleHeaderNo(): void {
		this._importConfiguration.isFirstLineHeader = false
		this.requestUpdate()
	}

	private _handleDuplicateToggle(event: Event): void {
		// Prevent event bubbling to avoid double firing
		event.stopPropagation()

		const toggle = event.target as any

		// Get the current value from the toggle
		let isEnabled = false

		// Check the toggle's value property first
		if (toggle.value === 'true' || toggle.value === true) {
			isEnabled = true
		} else if (toggle.value === 'false' || toggle.value === false) {
			isEnabled = false
		} else {
			// Fallback: check checked property or toggle current state
			isEnabled = toggle.checked === true ? true : !this._importConfiguration.isDuplicateCheckEnabled
		}

		// Update the configuration
		this._importConfiguration.isDuplicateCheckEnabled = isEnabled

		// Update the UI immediately
		this.requestUpdate()

		// Ensure the toggle reflects the correct state
		this._addTimeout(() => {
			toggle.value = isEnabled ? 'true' : 'false'
			toggle.setAttribute('value', isEnabled ? 'true' : 'false')
		}, 0)
	}

	private _handleDuplicateAction(event: Event): void {
		const button = event.target as any
		this._importConfiguration.actionForDuplicates = button.value
		this.requestUpdate()
	}

	private _handleColumnMappingCancel(): void {
		if (this.importPresets && this.importPresets.length > 0) {
			this._currentDialogStep = DialogStep.PRESET
		} else {
			this._currentDialogStep = DialogStep.PREVIEW
		}
	}

	private async _handleColumnMappingImport(): Promise<void> {
		this._currentDialogStep = DialogStep.CLOSED
		await this.executeImport()
	}

	// Error dialog handlers
	private _handleErrorDialogClose(): void {
		this._currentDialogStep = DialogStep.CLOSED
		this._errorsExcelBase64 = undefined
	}

	private _handleDownloadReport(): void {
		this.downloadErrorReport()
	}

	private _handleTryAgain(): void {
		this._currentDialogStep = DialogStep.CLOSED
		this._errorsExcelBase64 = undefined
		// Restart the import process
		this.startImport()
	}

	// Duplicate dialog handlers
	private async _handleUpdateAll(): Promise<void> {
		if (this._duplicateData && this._duplicateSummary) {
			this._currentDialogStep = DialogStep.CLOSED
			await this.reprocessDuplicates(this._duplicateData, this._duplicateSummary)
		}
	}

	private _handleSkipAll(): void {
		this._currentDialogStep = DialogStep.CLOSED
		if (this._duplicateSummary) {
			const summaryMessage = `${this._duplicateSummary.inserted || 0} imported, ${this._duplicateSummary.skipped || 0} skipped, ${this._duplicateSummary.updated || 0} updated`
			this.showSuccessToast(summaryMessage)
			this.onImportComplete?.(true)
		}
	}

	private _handleContinueImport(): void {
		// This will need to handle the logic from the original continue button
		// For now, just close the dialog
		this._currentDialogStep = DialogStep.CLOSED
		if (this._duplicateSummary) {
			const summaryMessage = `${this._duplicateSummary.inserted || 0} imported, ${this._duplicateSummary.skipped || 0} skipped, ${this._duplicateSummary.updated || 0} updated`
			this.showSuccessToast(summaryMessage)
			this.onImportComplete?.(true)
		}
	}

	private _renderColumnMappingTable() {
		if (!this._excelData) return ''

		const rowsData = this.prepareColumnMappingRows()

		// Create autocomplete options using Lit's html template system
		const autocompleteOptions = this.pageViewColumns.map(col => html`
			<lvl-option value="${col.key || ''}">${col.key || ''}</lvl-option>
		`)

		return html`
			<lvl-table
				class="import-column-mapping-table"
				selection-mode="multiple"
				borderless
				rows='${JSON.stringify(rowsData)}'
				style="width: 100%; height: 100%; border: none; border-collapse: collapse; background: var(--clr-background-lvl-0); table-layout: fixed !important;">
				<lvl-table-data-column name="name" label="${this.localize('column', 'Column')}"></lvl-table-data-column>
				<lvl-table-data-column name="arrow-right" label="" type="icon" width="3rem" maxWidth="3rem"></lvl-table-data-column>
				<lvl-table-data-column label="${this.localize('columnToPick', 'Column to pick from')}" type="component" width="20rem" minWidth="20rem" maxWidth="20rem">
					<lvl-autocomplete
						class="import-autocomplete"
						onchange="window.excelImporterInstance?.handleAutocompleteChangeFromHTML(this)"
						placeholder="${this.localize('pleaseChoose', 'please choose')}"
						text-align="left"
						type="string"
						column-view=""
						style="width: 18rem !important; min-width: 18rem !important; max-width: 18rem !important; display: inline-block !important;">
						<lvl-head></lvl-head>
						<lvl-body>
							<lvl-option value="">${this.localize('pleaseChoose', 'Please choose')}</lvl-option>
							${autocompleteOptions}
						</lvl-body>
					</lvl-autocomplete>
				</lvl-table-data-column>
				<lvl-table-data-column name="equals" label="" type="icon" width="3rem" maxWidth="3rem"></lvl-table-data-column>
				<lvl-table-data-column name="data_from_file" label="${this.localize('dataFromFile', 'Data from file')}"></lvl-table-data-column>
			</lvl-table>
		`
	}

	private showOverlay(text: string): void {
		// Emit event for parent to handle overlay instead of accessing global
		this.dispatchEvent(new CustomEvent('show-overlay', {
			detail: { text },
			bubbles: true,
			composed: true
		}))

		// Fallback to global if no parent handles the event (for backward compatibility)
		setTimeout(() => {
			if ((window as any).Overlay && (window as any).Overlay.showWait) {
				(window as any).Overlay.showWait(text)
			} else {
				// Fallback to direct element access
				const overlay = document.getElementById('overlay') as any
				if (overlay && overlay.showWait) {
					overlay.showWait(text)
				}
			}
		}, 0)
	}

	private hideOverlay(): void {
		// Emit event for parent to handle overlay instead of accessing global
		this.dispatchEvent(new CustomEvent('hide-overlay', {
			bubbles: true,
			composed: true
		}))

		// Fallback to global if no parent handles the event (for backward compatibility)
		setTimeout(() => {
			if ((window as any).Overlay && (window as any).Overlay.hideWait) {
				(window as any).Overlay.hideWait()
			} else {
				// Fallback to direct element access
				const overlay = document.getElementById('overlay') as any
				if (overlay && overlay.hideWait) {
					overlay.hideWait()
				}
			}
		}, 0)
	}

	private showErrorToast(message: string): void {
		// Try to use the toaster directly first
		const toaster = document.getElementById('toaster') as any

		if (toaster && toaster.notify) {
			toaster.notify({
				heading: this.localize('importError'),
				content: message,
				type: 'error'
			})
		} else {
			// Only emit event if direct toaster is not available
			this.dispatchEvent(new CustomEvent('show-toast', {
				detail: {
					heading: this.localize('importError'),
					text: message,
					type: 'error',
					duration: 5000
				},
				bubbles: true,
				composed: true
			}))
		}
	}

	private showSuccessToast(message: string): void {
		// Try to use the toaster directly first
		const toasterElement = document.getElementById('toaster') as any
		if (toasterElement && toasterElement.notify && typeof toasterElement.notify === 'function') {
			toasterElement.notify({
				heading: this.localize('importComplete'),
				content: message,
				type: 'success'
			})
		} else {
			// Only emit event if direct toaster is not available
			this.dispatchEvent(new CustomEvent('show-toast', {
				detail: {
					heading: this.localize('importComplete'),
					text: message,
					type: 'success',
					duration: 5000
				},
				bubbles: true,
				composed: true
			}))
		}
	}

	// Excel processing methods
	private async ensureXLSXLoaded(): Promise<void> {
		if (typeof (window as any).XLSX !== 'undefined') {
			return
		}

		return new Promise((resolve, reject) => {
			const script = document.createElement('script')
			script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js'
			script.onload = () => resolve()
			script.onerror = () => reject(new Error('Failed to load XLSX library'))
			document.head.appendChild(script)
		})
	}

	private async readExcelFile(file: File): Promise<ExcelData> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader()

			reader.onload = (e) => {
				try {
					const data = new Uint8Array(e.target?.result as ArrayBuffer)
					const workbook = (window as any).XLSX.read(data, { type: 'array' })

					const sheetNames = workbook.SheetNames
					const firstSheetName = sheetNames[0]
					const worksheet = workbook.Sheets[firstSheetName]
					const jsonData = (window as any).XLSX.utils.sheet_to_json(worksheet, {
						header: 1,
						defval: ''
					})

					resolve({
						data: jsonData,
						sheetNames: sheetNames,
						currentSheet: firstSheetName
					})
				} catch (error) {
					reject(new Error(`Failed to read Excel file: ${error}`))
				}
			}

			reader.onerror = () => {
				reject(new Error('Failed to read file'))
			}

			reader.readAsArrayBuffer(file)
		})
	}

	private validateExcelData(excelData: ExcelData): { isValid: boolean; errors: string[] } {
		const errors: string[] = []

		if (!excelData.data || excelData.data.length === 0) {
			errors.push('Excel file is empty')
		}

		if (excelData.data && excelData.data.length === 1) {
			errors.push('Excel file contains only header row')
		}

		return {
			isValid: errors.length === 0,
			errors
		}
	}

	private showPresetDialog(): void {
		if (!this.importPresets || this.importPresets.length === 0) {
			this._currentDialogStep = DialogStep.PREVIEW
			return
		}

		this._currentDialogStep = DialogStep.PRESET

		// Add event listener for option selection using component-scoped approach
		const optionSelectedHandler = (event: Event) => {
			const customEvent = event as CustomEvent
			const selectableCards = this.shadowRoot?.querySelector('lvl-selectable-cards')
			if (selectableCards && customEvent.detail) {
				selectableCards.setAttribute('selected-card', customEvent.detail.value)
			}
		}

		// Use component-scoped event listener instead of document
		this._addComponentEventListener(this, 'option-selected', optionSelectedHandler)
	}

	private showPreviewDialog(): void {
		if (!this._excelData) {
			this.showErrorToast('No Excel data available')
			return
		}

		this._currentDialogStep = DialogStep.PREVIEW
	}

	private generatePreviewData(excelData: ExcelData, isFirstLineHeader: boolean, maxRows: number = 3, maxColumns: number = 3): { headers: string[]; rows: any[] } {
		if (!excelData.data || excelData.data.length === 0) {
			return { headers: [], rows: [] }
		}

		let headers: string[] = []
		let dataStartIndex = 0

		if (isFirstLineHeader && excelData.data.length > 0) {
			headers = excelData.data[0].slice(0, maxColumns).map((header, index) =>
				header || `Column ${index + 1}`
			)
			dataStartIndex = 1
		} else {
			// Generate column names A, B, C, etc.
			const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
			const columnCount = Math.min(excelData.data[0]?.length || 0, maxColumns)
			headers = Array.from({ length: columnCount }, (_, i) => `Column ${letters[i]}`)
		}

		const rows: any[] = []
		const endIndex = Math.min(dataStartIndex + maxRows, excelData.data.length)

		for (let i = dataStartIndex; i < endIndex; i++) {
			const row = excelData.data[i]
			const rowData: any = {}

			headers.forEach((_, index) => {
				rowData[`col${index}`] = row && index < row.length ? row[index] : ''
			})

			rows.push(rowData)
		}

		return { headers, rows }
	}

	private closeCurrentDialog(): void {
		// Close dialog by setting step to closed
		this._currentDialogStep = DialogStep.CLOSED

		// Clean up global reference
		if ((window as any).excelImporterInstance === this) {
			delete (window as any).excelImporterInstance
		}

		// Clean up component event listeners
		this.cleanupComponentEventListeners()

		// Clean up document event listeners
		this._cleanupDocumentEventListeners()

		// Clear all timeouts
		this._clearAllTimeouts()

		this.cleanupObservers()
	}

	private cleanupObservers(): void {
		if (this._mutationObserver) {
			this._mutationObserver.disconnect()
			this._mutationObserver = undefined
		}
		if (this._dropdownObserver) {
			this._dropdownObserver.disconnect()
			this._dropdownObserver = undefined
		}

		// Clear selected values tracking
		this._selectedValues.clear()
	}



	private prepareColumnMappingRows(): any[] {
		if (!this._excelData || !this._excelData.data || this._excelData.data.length === 0) {
			return [
				{ "name": "Column A", "data_from_file": "" },
				{ "name": "Column B", "data_from_file": "" },
				{ "name": "Column C", "data_from_file": "" }
			]
		}

		let rowsData = []

		if (this._importConfiguration.isFirstLineHeader && this._excelData.data.length > 0) {
			// Use first row as headers if isFirstLineHeader is true
			const headerRow = this._excelData.data[0]
			// Get second row for data preview if available
			const dataRow = this._excelData.data.length > 1 ? this._excelData.data[1] : null

			if (headerRow && headerRow.length > 0) {
				// Create row entries from the first row (header) values
				for (let i = 0; i < Math.min(headerRow.length, 10); i++) {
					rowsData.push({
						"name": headerRow[i] || `Column ${i + 1}`,
						"data_from_file": dataRow && i < dataRow.length ? dataRow[i] : ""
					})
				}
			}
		} else {
			// Use Excel column naming (A, B, C...) if isFirstLineHeader is false
			const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
			const columnCount = this._excelData.data[0] ? this._excelData.data[0].length : 10

			// When not using header row, first row is data
			const dataRow = this._excelData.data.length > 0 ? this._excelData.data[0] : null

			for (let i = 0; i < Math.min(columnCount, 26); i++) {
				rowsData.push({
					"name": `Column ${letters[i]}`,
					"data_from_file": dataRow && i < dataRow.length ? dataRow[i] : ""
				})
			}
		}

		return rowsData
	}


	private setupColumnMappingEventListeners(): void {
		// Try multiple selectors to find the dialog
		const dialogSelectors = [
			'lvl-dialog[name="import-dialog"]',
			'lvl-dialog.import-dialog',
			'lvl-dialog'
		]

		let dialogBox: Element | null = null
		for (const selector of dialogSelectors) {
			dialogBox = document.querySelector(selector)
			if (dialogBox) {
				break
			}
		}

		// Also check within this component's shadow root
		if (!dialogBox && this.shadowRoot) {
			for (const selector of dialogSelectors) {
				dialogBox = this.shadowRoot.querySelector(selector)
				if (dialogBox) {
					break
				}
			}
		}

		if (!dialogBox) {
			return
		}

		const cancelButton = dialogBox.querySelector('[data-action="cancel"]')
		const importButton = dialogBox.querySelector('[data-action="import"]')

		// Cancel button
		if (cancelButton) {
			(cancelButton as HTMLElement).onclick = () => {
				this.closeCurrentDialog()
			}
		}

		// Import button
		if (importButton) {
			(importButton as HTMLElement).onclick = () => {
				this.handleImportButtonClick()
			}
		}

		// Note: Toggle functionality is now handled by the Lit component's reactive rendering
		// The _handleDuplicateToggle method handles the state changes and requestUpdate() triggers re-render

		// Duplicate action button group
		const duplicateButtonGroup = dialogBox.querySelector('lvl-button-group[name="duplicate-action"]')
		if (duplicateButtonGroup) {
			duplicateButtonGroup.addEventListener('change', () => {
				this._importConfiguration.actionForDuplicates = (duplicateButtonGroup as HTMLElement).getAttribute('value')
			})
		}

		// Set up autocomplete event listeners for column mapping
		this.setupAutocompleteEventListeners(dialogBox)
	}



	private setupDropdownObserver(): void {
		// Set up a MutationObserver to watch for dropdown content changes
		this._dropdownObserver = new MutationObserver(() => {
			// Look for open popup
			const popup = document.querySelector('lvl-popup[name="autocomplete-dropdown"][open]')
			if (popup) {
				// Find the related autocomplete
				const dialogBox = document.querySelector('lvl-dialog[name="import-dialog"]')
				if (!dialogBox) return

				const autocompletes = dialogBox.querySelectorAll('lvl-autocomplete')
				let activeAutocomplete: any = null

				// Try to find which autocomplete is active
				autocompletes.forEach((autocomplete) => {
					if (autocomplete.contains(document.activeElement) || autocomplete === document.activeElement) {
						activeAutocomplete = autocomplete
					}
				})

				if (activeAutocomplete) {
					setTimeout(() => {
						if (activeAutocomplete) {
							this.modifyDropdownOptions(activeAutocomplete)
						}
					}, 50)
				}
			}
		})

		// Observe the body for changes to catch when dropdowns are added
		this._dropdownObserver.observe(document.body, { childList: true, subtree: true })
	}

	private updateAvailableOptions(): void {
		// Hide already selected options in all autocomplete dropdowns
		const dialogBox = this.shadowRoot?.querySelector('lvl-dialog[name="import-dialog"]') ||
						  document.querySelector('lvl-dialog[name="import-dialog"]')
		if (!dialogBox) {
			return
		}

		const autocompletes = dialogBox.querySelectorAll('lvl-autocomplete')

		autocompletes.forEach((autocomplete) => {
			this.modifyDropdownOptions(autocomplete)
		})
	}

	private modifyDropdownOptions(autocomplete: any): void {
		if (!autocomplete) {
			return
		}

		// Get the current value of this autocomplete
		const currentValue = autocomplete.value || autocomplete.getAttribute('value')

		// Find all options in this autocomplete
		const options = autocomplete.querySelectorAll('lvl-option')

		options.forEach((option: any) => {
			const value = option.getAttribute('value')
			if (value && value.trim() !== '' && this._selectedValues.has(value)) {
				// Don't hide the option if it's the current value of this autocomplete
				if (value === currentValue) {
					option.style.display = ''
				} else {
					// Hide already selected options (but not the current one)
					option.style.display = 'none'
				}
			} else {
				// Show available options
				option.style.display = ''
			}
		})
	}

	private setupAutocompleteEventListeners(dialogBox: Element): void {
		// Use event delegation to handle autocomplete changes since @change handlers don't work with unsafeHTML
		const delegatedChangeHandler = (event: Event) => {
			const target = event.target as HTMLElement
			if (target && target.tagName === 'LVL-AUTOCOMPLETE' && target.closest('lvl-dialog[name="import-dialog"]')) {
				this.autoSelectCheckbox(target)
				this.trackSelectedValue(target)
			}
		}

		// Add event delegation for change events on the dialog
		dialogBox.addEventListener('change', delegatedChangeHandler, true) // Use capture phase

		// Also add to document as fallback - track this for cleanup
		this._addDocumentEventListener('change', delegatedChangeHandler)

		// Set up MutationObserver to watch for autocomplete value changes
		this._mutationObserver = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'attributes') {
					const target = mutation.target

					// Watch for value changes on autocomplete elements
					if (target instanceof Element && target.tagName === 'LVL-AUTOCOMPLETE' && mutation.attributeName === 'value') {
						this.autoSelectCheckbox(target)
						this.trackSelectedValue(target)
					}

					// Also watch for value changes on input elements inside autocompletes
					if (target instanceof Element && target.tagName === 'INPUT' && mutation.attributeName === 'value') {
						const autocomplete = target.closest('lvl-autocomplete')
						if (autocomplete) {
							// Update the autocomplete value
							;(autocomplete as any).value = target.getAttribute('value') || ''
							this.autoSelectCheckbox(autocomplete)
							this.trackSelectedValue(autocomplete)
						}
					}
				}

				// Watch for property changes (not just attributes)
				if (mutation.type === 'childList') {
					// Check if any autocomplete elements were added/modified
					mutation.addedNodes.forEach(node => {
						if (node instanceof Element) {
							const autocompletes = node.querySelectorAll ? node.querySelectorAll('lvl-autocomplete') : []
							if (autocompletes.length > 0) {
								// Set up listeners for newly added autocompletes
								this.setupAutocompleteEventListeners(dialogBox)
							}
						}
					})
				}
			})
		})

		// Observe the entire dialog for changes with comprehensive options
		this._mutationObserver.observe(dialogBox, {
			attributes: true,
			childList: true,
			subtree: true,
			attributeFilter: ['value', 'selected'],
			attributeOldValue: true
		})

		// Add table click event listener for option selection
		const table = dialogBox.querySelector('lvl-table')
		if (table) {
			table.addEventListener('click', (event) => {
				const target = event.target as HTMLElement
				if (!target) return
				const option = target.closest('lvl-option')
				if (option) {
					const value = option.getAttribute('value')
					const row = option.closest('.table__row')

					// Find the autocomplete in the same row and auto-select the checkbox
					if (row) {
						const autocomplete = row.querySelector('lvl-autocomplete')
						if (autocomplete && value) {
							// Set the autocomplete value
							;(autocomplete as any).value = value

							// Also set the attribute to ensure it's properly set
							autocomplete.setAttribute('value', value)

							// Dispatch a change event to trigger our listeners
							autocomplete.dispatchEvent(new Event('change', { bubbles: true }))

							// Call our methods directly as well
							this.autoSelectCheckbox(autocomplete)
							this.trackSelectedValue(autocomplete)
						}
					}
				}
			})
		}

		// Add direct event listeners to existing autocompletes as additional fallback
		const autocompletes = dialogBox.querySelectorAll('lvl-autocomplete')

		autocompletes.forEach((autocomplete) => {
			// Multiple event types to catch autocomplete changes
			const eventTypes = ['change', 'input', 'blur', 'autocomplete-change', 'value-changed']

			eventTypes.forEach(eventType => {
				autocomplete.addEventListener(eventType, () => {
					this.autoSelectCheckbox(autocomplete)
					this.trackSelectedValue(autocomplete)
				})
			})

			// Also listen to the input element inside the autocomplete shadow DOM
			if (autocomplete.shadowRoot) {
				const input = autocomplete.shadowRoot.querySelector('input')
				if (input) {
					input.addEventListener('change', () => {
						// Update the autocomplete value and trigger our handler
						;(autocomplete as any).value = input.value
						this.autoSelectCheckbox(autocomplete)
						this.trackSelectedValue(autocomplete)
					})

					input.addEventListener('blur', () => {
						// Update the autocomplete value and trigger our handler
						;(autocomplete as any).value = input.value
						this.autoSelectCheckbox(autocomplete)
						this.trackSelectedValue(autocomplete)
					})
				}
			}

			// Add click and focus event listeners for dropdown handling
			autocomplete.addEventListener('click', () => {
				setTimeout(() => {
					this.updateAvailableOptions()
					this.modifyDropdownOptions(autocomplete)
				}, 100)
			})

			autocomplete.addEventListener('focus', () => {
				setTimeout(() => {
					this.updateAvailableOptions()
					this.modifyDropdownOptions(autocomplete)
				}, 100)
			})
		})
	}

	// Component-scoped autocomplete handling - no window/DOM dependencies
	private _autocompleteChangeHandler: ((event: Event) => void) | null = null
	private _autocompleteObserver: MutationObserver | null = null



	private cleanupAutocompleteHandling(): void {
		// Remove event listener
		if (this._autocompleteChangeHandler) {
			const dialogBox = this.shadowRoot?.querySelector('lvl-dialog[name="import-dialog"]')
			if (dialogBox) {
				dialogBox.removeEventListener('change', this._autocompleteChangeHandler, true)
			}
			this._autocompleteChangeHandler = null
		}

		// Disconnect MutationObserver
		if (this._autocompleteObserver) {
			this._autocompleteObserver.disconnect()
			this._autocompleteObserver = null
		}
	}

	// Public method for handling autocomplete changes - called from HTML onchange attribute
	public handleAutocompleteChange(autocomplete: any): void {
		// Call our methods to handle the change
		this.autoSelectCheckbox(autocomplete)
		this.trackSelectedValue(autocomplete)
	}

	// Public method that can be called from HTML onchange attribute
	public handleAutocompleteChangeFromHTML(autocomplete: any): void {
		// Call our methods to handle the change
		this.autoSelectCheckbox(autocomplete)
		this.trackSelectedValue(autocomplete)

		// Also dispatch a change event for other listeners
		document.dispatchEvent(new Event('change'))
	}


	private autoSelectCheckbox = (autocomplete: any): void => {
		if (!autocomplete) {
			return
		}

		// Find the row containing the autocomplete
		const row = autocomplete.closest('.table__row')
		const rowIndex = row ? row.getAttribute('data-position') : null

		if (!rowIndex) {
			return
		}

		// Find the table and access its shadow DOM - use same logic as setup
		const dialogSelectors = [
			'lvl-dialog[name="import-dialog"]',
			'lvl-dialog.import-dialog',
			'lvl-dialog'
		]

		let dialogBox: Element | null = null
		for (const selector of dialogSelectors) {
			dialogBox = document.querySelector(selector)
			if (dialogBox) break
		}

		// Also check within this component's shadow root
		if (!dialogBox && this.shadowRoot) {
			for (const selector of dialogSelectors) {
				dialogBox = this.shadowRoot.querySelector(selector)
				if (dialogBox) {
					break
				}
			}
		}

		if (!dialogBox) {
			return
		}

		const lvlTable = dialogBox.querySelector('lvl-table[selection-mode="multiple"][borderless]')
		if (!lvlTable || !lvlTable.shadowRoot) {
			return
		}

		// Find the corresponding row in the shadow DOM
		const tableRow = lvlTable.shadowRoot.querySelector(`.table__row[data-position="${rowIndex}"]`)
		if (!tableRow) {
			return
		}

		// Find the checkbox in the row
		const checkbox = tableRow.querySelector('lvl-checkbox')
		if (!checkbox) {
			return
		}

		// Try different ways to get the current value
		let currentValue = null

		// Method 1: Try the value property
		if (autocomplete.value && autocomplete.value !== 'null') {
			currentValue = autocomplete.value
		}

		// Method 2: Try the getAttribute method
		if (!currentValue) {
			const attrValue = autocomplete.getAttribute('value')
			if (attrValue && attrValue !== 'null' && attrValue.trim() !== '') {
				currentValue = attrValue
			}
		}

		// Method 3: Try to find the selected option
		if (!currentValue) {
			const selectedOption = autocomplete.querySelector('lvl-option[selected]')
			if (selectedOption) {
				const optionValue = selectedOption.getAttribute('value')
				if (optionValue && optionValue.trim() !== '') {
					currentValue = optionValue
				}
			}
		}

		// Check if there's a valid value selected
		const hasValue = currentValue && currentValue !== 'null' && currentValue.trim() !== ''

		if (hasValue) {
			// Set the checkbox as checked
			checkbox.setAttribute('checked', '')
			;(checkbox as any).checked = true

			// Dispatch a change event to ensure the table updates its internal state
			const changeEvent = new CustomEvent('checkbox-change', {
				detail: { checked: true },
				bubbles: true,
				composed: true
			})
			checkbox.dispatchEvent(changeEvent)

		} else {
			// Uncheck the checkbox if the value is empty
			checkbox.removeAttribute('checked')
			;(checkbox as any).checked = false

			// Dispatch a change event to ensure the table updates its internal state
			const changeEvent = new CustomEvent('checkbox-change', {
				detail: { checked: false },
				bubbles: true,
				composed: true
			})
			checkbox.dispatchEvent(changeEvent)
		}
	}

	private trackSelectedValue = (autocomplete: any): void => {
		if (!autocomplete) {
			return
		}

		// First, rebuild the selected values set by checking all autocompletes
		this.rebuildSelectedValuesSet()

		// Update available options to hide already selected values
		setTimeout(() => {
			this.updateAvailableOptions()
		}, 50)
	}

	private rebuildSelectedValuesSet(): void {
		// Clear the current set
		this._selectedValues.clear()

		// Find all autocompletes in the dialog and add their values to the set
		const dialogBox = this.shadowRoot?.querySelector('lvl-dialog[name="import-dialog"]') ||
						  document.querySelector('lvl-dialog[name="import-dialog"]')
		if (!dialogBox) return

		const autocompletes = dialogBox.querySelectorAll('lvl-autocomplete')
		autocompletes.forEach((autocomplete: any) => {
			const value = autocomplete.value || autocomplete.getAttribute('value')
			if (value && value.trim() !== '' && value !== 'null') {
				this._selectedValues.add(value)
			}
		})
	}

	private async handleImportButtonClick(): Promise<void> {
		// Extract mappings from the table
		this.extractMappingsFromTable()

		// Validate that at least one column is mapped
		if (this._importConfiguration.selectedMappings.length === 0) {
			this.showErrorToast(this.localize('selectColumnsError'))
			return
		}

		this.closeCurrentDialog()
		await this.executeImport()
	}

	private extractMappingsFromTable(): void {
		// Initialize selectedMappings array
		this._importConfiguration.selectedMappings = []

		const dialogBox = this.shadowRoot?.querySelector('lvl-dialog[name="import-dialog"]') ||
						  document.querySelector('lvl-dialog[name="import-dialog"]')
		if (!dialogBox) return

		const lvlTable = dialogBox.querySelector('lvl-table[selection-mode="multiple"][borderless]')
		const shadowRoot = lvlTable?.shadowRoot
		if (shadowRoot) {
			const tableRows = shadowRoot.querySelectorAll('.table__row')

			tableRows.forEach(row => {
				const checkbox = row.querySelector('lvl-checkbox')
				if (checkbox && checkbox.hasAttribute('checked')) {
					const autocomplete = row.querySelector('lvl-autocomplete')
					if (autocomplete && autocomplete.value && autocomplete.value.trim() !== '') {
						const columnKey = autocomplete.value
						const index = row.getAttribute('data-position')

						const mapping: FieldMapping = {
							columnKey: columnKey,
							index: parseInt(index || '0'),
							dataType: '' // We'll determine this from pageViewColumns if needed
						}

						this._importConfiguration.selectedMappings.push(mapping)
					}
				}
			})
		}
	}

	private async executeImport(): Promise<void> {
		// Prevent multiple simultaneous imports from the same component
		if (this._isImporting) {
			return
		}

		if (!this._excelData) {
			this.showErrorToast('No Excel data available')
			return
		}

		this._isImporting = true
		const processId = uuid()

		try {
			// Notify parent component
			this.onImportStart?.()

			// Use only the global ProgressSocket to avoid dual WebSocket connections
			// The event-based approach was causing duplicate WebSockets
			let progressSocket: any = null
			const ProgressSocket = (window as any).ProgressSocket
			if (ProgressSocket) {
				progressSocket = new ProgressSocket()
				progressSocket.startProcess(processId, {
					title: this.localize('importData'),
					description: this.localize('importWaitMessage'),
					icon: 'file-import'
				})
				this._progressSocket = progressSocket
			}

			// Prepare import parameters
			const importParams = {
				actionForDuplicates: this._importConfiguration.isDuplicateCheckEnabled === false ? null : this._importConfiguration.actionForDuplicates,
				isDuplicateCheckEnabled: this._importConfiguration.isDuplicateCheckEnabled,
				isFirstLineHeader: this._importConfiguration.isFirstLineHeader,
				dataSourceId: this.getDataSourceId(),
				matchBy: this.pageViewColumns.map(col => col.key), // Column keys for duplicate matching
				selectedMappings: this._importConfiguration.selectedMappings
			}

			// Convert Excel data back to binary format for upload
			const excelBuffer = (window as any).XLSX.write(this.createWorkbookFromData(), {
				bookType: 'xlsx',
				type: 'array'
			})

			// Create a Blob from the buffer
			const excelBlob = new Blob([excelBuffer], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			})

			// Create FormData to send both JSON and file data
			const formData = new FormData()
			formData.append('importParams', JSON.stringify(importParams))
			formData.append('excelFile', excelBlob, this._originalFileName || 'import.xlsx')
			formData.append('isFirstLineHeader', String(this._importConfiguration.isFirstLineHeader))

			// Set up abort controller for timeout
			const controller = new AbortController()
			const timeoutId = this._addTimeout(() => controller.abort(), 300000) // 5 minutes timeout

			// Make the import API call
			const response = await fetch('/Api/PageViews/ExcelImport', {
				method: 'POST',
				body: formData,
				headers: {
					'Accept': 'application/json',
					'X-Process-ID': processId
				},
				signal: controller.signal
			})

			clearTimeout(timeoutId)

			if (!response.ok) {
				let errorMessage = this.localize('importErrorGeneric')
				try {
					const errorData = await response.json()
					errorMessage = errorData.message || errorData.error || errorMessage
				} catch (e) {
					errorMessage = response.statusText || errorMessage
				}

				// Update progress socket directly
				if (this._progressSocket) {
					this._progressSocket.updateModalStatus(errorMessage)
					this._progressSocket.updateModalProgress(0)
				}

				this.showErrorToast(errorMessage)
				this.onImportComplete?.(false)
				return
			}

			// Handle successful response
			const result = await response.json()

			// Reload the data view if available
			const multiView = document.querySelector('lvl-multi-data-view') as any
			if (multiView && multiView.reload) {
				multiView.reload()
			}

			// Handle the result - check for errors more robustly
			const errors = result.data?.errors
			const hasErrors = errors && Array.isArray(errors) && errors.length > 0

			// Store import result data
			this._importErrors = result.data?.errors || []
			this._errorsExcelBase64 = result.data?.errorsExcelBase64
			this._fileEntityId = result.data?.fileEntityId

			if (!hasErrors) {
				// Successful import (no errors or empty errors array)
				if (result.data?.duplicates?.length > 0 && this._importConfiguration.isDuplicateCheckEnabled) {
					// Handle duplicates - show duplicate dialog
					this.showDuplicateDialog(result.data.duplicates, result.data.summary)
					return // Don't call onImportComplete yet, wait for duplicate resolution
				} else {
					this.showImportSuccessMessage(result.data.summary)
				}
			} else {
				// Import with errors
				// Check if there are also duplicates to handle
				if (result.data?.duplicates?.length > 0 && this._importConfiguration.isDuplicateCheckEnabled) {
					// Show duplicate dialog even with errors
					this.showDuplicateDialog(result.data.duplicates, result.data.summary)
					return // Don't call onImportComplete yet, wait for duplicate resolution
				} else {
					this.showImportErrorMessage(result.data)
				}
			}

			this.onImportComplete?.(true)

		} catch (error) {
			let errorMessage = this.localize('importErrorGeneric')

			if (error instanceof Error) {
				if (error.name === 'AbortError') {
					errorMessage = this.localize('importTimeout')
				} else if (error.message) {
					errorMessage = error.message
				}
			}

			// Update progress socket directly
			if (this._progressSocket) {
				this._progressSocket.updateModalStatus(errorMessage)
				this._progressSocket.updateModalProgress(0)
			}

			this.showErrorToast(errorMessage)
			this.onImportComplete?.(false)
		} finally {
			// Reset import flag
			this._isImporting = false

			// Cleanup progress socket
			this._addTimeout(() => {
				if (this._progressSocket) {
					this._progressSocket.destroy()
					this._progressSocket = undefined
				}
			}, 2000)
		}
	}

	private createWorkbookFromData(): any {
		if (!this._excelData) {
			throw new Error('No Excel data available')
		}

		// Create a new workbook
		const workbook = (window as any).XLSX.utils.book_new()

		// Convert the data array to a worksheet
		const worksheet: any = (window as any).XLSX.utils.aoa_to_sheet(this._excelData.data)

		// Add the worksheet to the workbook
		;(window as any).XLSX.utils.book_append_sheet(workbook, worksheet, this._excelData.currentSheet || 'Sheet1')

		return workbook
	}

	private showImportSuccessMessage(summary: any): void {
		const message = `${summary.inserted || 0} imported, ${summary.skipped || 0} skipped, ${summary.updated || 0} updated`

		// Use the simplified toast system to avoid duplicates
		this.showSuccessToast(message)
	}

	private showImportErrorMessage(data: any): void {
		const summary = data.summary || {}
		const message = `Import completed with issues. ${summary.inserted || 0} imported, ${summary.skipped || 0} skipped, ${summary.updated || 0} updated, ${summary.failed || 0} failed`

		// Store errors for potential error dialog
		this._importErrors = data.errors || []
		this._errorsExcelBase64 = data.errorsExcelBase64

		// Only show the toast notification with buttons - do NOT auto-open the error dialog
		// The error dialog will only open when user clicks "View Report" button
		this.showErrorToastWithButtons(message, data.errors || [])
	}

	private showErrorToastWithButtons(message: string, _errors: any[]): void {
		const toasterElement = document.getElementById('toaster') as any

		if (toasterElement && toasterElement.notify) {
			toasterElement.notify({
				heading: this.localize('importError'),
				content: message,
				type: 'error',
				permanent: true,
				buttons: [
					{
						label: this.localize('cancel', 'Cancel'),
						type: 'secondary',
						color: 'active',
						closeOnClick: true
					},
					{
						label: this.localize('viewReport', 'View Report'),
						type: 'primary',
						closeOnClick: true,
						onclick: () => this.showErrorDialog()
					}
				]
			})
		} else {
			// Final fallback - use simple error toast without buttons if toaster is not available
			// Emit event for parent to handle toast instead of accessing global
			this.dispatchEvent(new CustomEvent('show-toast', {
				detail: {
					heading: this.localize('importError'),
					text: message,
					type: 'error',
					duration: 5000
				},
				bubbles: true,
				composed: true
			}))
		}
	}

	public showErrorDialog(): void {
		// Use state-based dialog instead of creating DOM elements
		this._currentDialogStep = DialogStep.ERROR
	}

	private downloadErrorReport(): void {
		// Handle download report action
		if (!this._errorsExcelBase64) {
			return
		}

		try {
			// Convert Base64 to Blob
			const byteCharacters = atob(this._errorsExcelBase64)
			const byteNumbers = new Array(byteCharacters.length)
			for (let i = 0; i < byteCharacters.length; i++) {
				byteNumbers[i] = byteCharacters.charCodeAt(i)
			}
			const byteArray = new Uint8Array(byteNumbers)
			const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

			// Create a link element
			const link = document.createElement('a')
			link.href = URL.createObjectURL(blob)
			link.download = 'Import error report.xlsx'
			link.style.display = 'none'

			// Append link to body and trigger download
			document.body.appendChild(link)
			link.click()

			// Clean up
			document.body.removeChild(link)
			URL.revokeObjectURL(link.href)
		} catch (error) {
			this.showErrorToast(this.localize('downloadError', 'Error downloading report'))
		}
	}

	private showDuplicateDialog(duplicates: DuplicateItem[], summary: any): void {
		// Store data in state and show dialog
		this._duplicateData = duplicates
		this._duplicateSummary = summary
		this._currentDialogStep = DialogStep.DUPLICATE
	}


	private async reprocessDuplicates(duplicates: DuplicateItem[], summary: any): Promise<void> {
		// Prevent multiple simultaneous duplicate processing
		if (this._isImporting) {
			return
		}

		// Dialog is already closed by state management
		this._isImporting = true
		const processId = uuid()

		// Use only the global ProgressSocket to avoid dual WebSocket connections
		// The event-based approach was causing duplicate WebSockets
		let progressSocket: any = null
		const ProgressSocket = (window as any).ProgressSocket
		if (ProgressSocket) {
			progressSocket = new ProgressSocket()
			progressSocket.startProcess(processId, {
				title: this.localize('importData', 'Importing Data'),
				description: this.localize('importWaitMessage', 'Please wait while we import your file'),
				icon: 'file-import'
			})
		}

		const dataSourceId = this.getDataSourceId()
		try {
			const response = await fetch('/Api/PageViews/ExcelImport/UpdateDuplicate', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'X-Process-ID': processId
				},
				body: JSON.stringify({
					dataSourceId,
					duplicates,
					fileEntityId: this._fileEntityId // Include the fileEntityId from the initial import
				})
			})

			if (!response.ok) {
				// Update progress socket directly
				if (progressSocket) {
					progressSocket.updateModalStatus('Failed to update duplicates')
					progressSocket.updateModalProgress(0)
				}

				this.showErrorToast(this.localize('failedToUpdateDuplicates', 'Failed to update duplicates'))
				return
			}

			const result = await response.json()

			// Cleanup progress socket
			if (progressSocket) {
				progressSocket.destroy()
			}

			const summaryMessage = `${summary.inserted || 0} imported, ${(summary.skipped || 0) - (result.data?.summary?.updated || 0)} skipped, ${result.data?.summary?.updated || 0} updated`
			this.showSuccessToast(summaryMessage)

			// Reload the data view if available
			const multiView = document.querySelector('lvl-multi-data-view') as any
			if (multiView && multiView.reload) {
				multiView.reload()
			}

			// Complete the import process
			this.onImportComplete?.(true)

		} catch (error) {
			// Update and cleanup progress socket directly
			if (progressSocket) {
				progressSocket.updateModalStatus('Error reprocessing duplicates')
				progressSocket.updateModalProgress(0)
				progressSocket.destroy()
			}

			this.showErrorToast(this.localize('failedToUpdateDuplicates', 'Failed to update duplicates'))
			throw error
		} finally {
			// Reset import flag
			this._isImporting = false
		}
	}

	//#endregion
}



// Define a connection between html element tag and LitElement class
declare global {
	interface HTMLElementTagNameMap {
		'lvl-excel-importer': ExcelImporter
	}
}